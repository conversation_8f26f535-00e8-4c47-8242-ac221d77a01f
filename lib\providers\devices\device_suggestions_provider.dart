import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/services/database_service.dart';
import 'package:workshop_studio/services/device_suggestions_service.dart';

final deviceSuggestionsServiceProvider = Provider<DeviceSuggestionsService>((ref) {
  final dbService = ref.read(mysqlServiceProvider);
  return DeviceSuggestionsService(dbService);
});

final deviceSuggestionsProvider = FutureProvider<Map<String, List<String>>>((ref) async {
  final suggestionsService = ref.read(deviceSuggestionsServiceProvider);
  return await suggestionsService.getAllSuggestions();
});
